// 测试导入是否正常工作
console.log('开始测试导入...')

try {
  // 测试从 db.js 导入函数
  import { getCurrentUserId, generatePrivateChatId, addTabItem, getChatMessages } from '../utils/db.js'
    .then((module) => {
      console.log('✓ 成功导入 db.js 模块:', Object.keys(module))
      
      // 测试 getCurrentUserId 函数
      if (typeof module.getCurrentUserId === 'function') {
        console.log('✓ getCurrentUserId 函数可用')
        const userId = module.getCurrentUserId()
        console.log('当前用户ID:', userId)
      } else {
        console.error('✗ getCurrentUserId 不是函数')
      }
      
      // 测试 generatePrivateChatId 函数
      if (typeof module.generatePrivateChatId === 'function') {
        console.log('✓ generatePrivateChatId 函数可用')
        const chatId = module.generatePrivateChatId(123, 456)
        console.log('生成的私聊ID:', chatId)
      } else {
        console.error('✗ generatePrivateChatId 不是函数')
      }
      
      // 测试其他函数
      console.log('addTabItem 类型:', typeof module.addTabItem)
      console.log('getChatMessages 类型:', typeof module.getChatMessages)
    })
    .catch((error) => {
      console.error('✗ 导入 db.js 失败:', error)
    })

  // 测试从 chatListManager.js 导入
  import('../utils/chatListManager.js')
    .then((module) => {
      console.log('✓ 成功导入 chatListManager.js 模块:', Object.keys(module))
    })
    .catch((error) => {
      console.error('✗ 导入 chatListManager.js 失败:', error)
    })

} catch (error) {
  console.error('导入测试失败:', error)
}
