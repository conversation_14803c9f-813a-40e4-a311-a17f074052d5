<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket消息修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>WebSocket消息修复测试</h1>
    <p>此测试页面用于验证WebSocket消息接收和存储的修复是否正确工作。</p>

    <div class="test-section">
        <h2>测试说明</h2>
        <div class="info test-result">
            <strong>修复内容：</strong>
            <ul>
                <li>WebSocket接收消息时，检查是否是自己发送的消息，如果是则跳过存储（避免重复存储）</li>
                <li>私聊消息使用双向唯一ID作为chatid</li>
                <li>群聊消息使用群组ID作为chatid</li>
                <li>发送消息后正确更新聊天列表的最后消息信息</li>
                <li>修复聊天列表数据库索引问题</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>模拟测试</h2>
        <button onclick="simulatePrivateMessage()">模拟接收私聊消息</button>
        <button onclick="simulateGroupMessage()">模拟接收群聊消息</button>
        <button onclick="simulateOwnMessage()">模拟接收自己的消息</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="testLog" class="log"></div>
    </div>

    <div class="test-section">
        <h2>数据库检查</h2>
        <button onclick="checkDatabase()">检查数据库状态</button>
        <button onclick="checkChatList()">检查聊天列表</button>
        
        <div id="dbLog" class="log"></div>
    </div>

    <script>
        let testLog = document.getElementById('testLog');
        let dbLog = document.getElementById('dbLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testLog.textContent += `[${timestamp}] ${message}\n`;
            testLog.scrollTop = testLog.scrollHeight;
        }

        function dbLogMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            dbLog.textContent += `[${timestamp}] ${message}\n`;
            dbLog.scrollTop = dbLog.scrollHeight;
        }

        function clearLog() {
            testLog.textContent = '';
            dbLog.textContent = '';
        }

        // 模拟当前用户ID
        const currentUserId = '123';
        localStorage.setItem('userId', currentUserId);

        function simulatePrivateMessage() {
            log('模拟接收私聊消息（来自其他用户）');
            
            const mockMessage = {
                id: Date.now().toString(),
                typecode: 1,
                typecode2: 0,
                fromid: 456, // 其他用户ID
                toid: parseInt(currentUserId),
                msg: 'SGVsbG8gV29ybGQ=', // "Hello World" 的Base64编码
                t: new Date().toISOString(),
                nickname: '测试用户456',
                senderNickname: '测试用户456',
                avatar: '',
                senderAvatar: ''
            };

            // 模拟WebSocket消息处理
            log(`消息数据: ${JSON.stringify(mockMessage, null, 2)}`);
            log('检查是否是自己的消息...');
            
            if (String(mockMessage.fromid) === String(currentUserId)) {
                log('✓ 这是自己的消息，应该跳过存储');
            } else {
                log('✓ 这是其他用户的消息，应该存储到数据库');
            }
        }

        function simulateGroupMessage() {
            log('模拟接收群聊消息（来自其他用户）');
            
            const mockMessage = {
                id: Date.now().toString(),
                typecode: 2,
                typecode2: 0,
                fromid: 789, // 其他用户ID
                toid: 0,
                groupID: 100,
                msg: 'R3JvdXAgTWVzc2FnZQ==', // "Group Message" 的Base64编码
                t: new Date().toISOString(),
                nickname: '群聊测试用户',
                senderNickname: '群聊测试用户',
                avatar: '',
                senderAvatar: ''
            };

            log(`消息数据: ${JSON.stringify(mockMessage, null, 2)}`);
            log('检查是否是自己的消息...');
            
            if (String(mockMessage.fromid) === String(currentUserId)) {
                log('✓ 这是自己的消息，应该跳过存储');
            } else {
                log('✓ 这是其他用户的消息，应该存储到数据库');
            }
        }

        function simulateOwnMessage() {
            log('模拟接收自己发送的消息（WebSocket回传）');
            
            const mockMessage = {
                id: Date.now().toString(),
                typecode: 1,
                typecode2: 0,
                fromid: parseInt(currentUserId), // 自己的用户ID
                toid: 456,
                msg: 'TXkgTWVzc2FnZQ==', // "My Message" 的Base64编码
                t: new Date().toISOString(),
                nickname: '我',
                senderNickname: '我',
                avatar: '',
                senderAvatar: ''
            };

            log(`消息数据: ${JSON.stringify(mockMessage, null, 2)}`);
            log('检查是否是自己的消息...');
            
            if (String(mockMessage.fromid) === String(currentUserId)) {
                log('✓ 这是自己的消息，应该跳过存储（避免重复）');
            } else {
                log('✗ 这不是自己的消息，应该存储到数据库');
            }
        }

        function checkDatabase() {
            dbLogMessage('检查数据库状态...');

            // 检查IndexedDB是否可用
            if (!window.indexedDB) {
                dbLogMessage('✗ IndexedDB 不可用');
                return;
            }

            dbLogMessage('✓ IndexedDB 可用');

            // 尝试打开聊天数据库
            const request = indexedDB.open('chatDb', 2); // 使用正确的数据库名称和版本

            request.onsuccess = function(event) {
                const db = event.target.result;
                dbLogMessage(`✓ 聊天数据库打开成功，版本: ${db.version}`);
                dbLogMessage(`数据库包含的表: ${Array.from(db.objectStoreNames).join(', ')}`);
                db.close();
            };

            request.onerror = function(event) {
                dbLogMessage(`✗ 聊天数据库打开失败: ${event.target.error}`);
            };
        }

        function checkChatList() {
            dbLogMessage('检查聊天列表数据库...');
            
            const request = indexedDB.open('ChatListDB', 1);
            
            request.onsuccess = function(event) {
                const db = event.target.result;
                dbLogMessage(`✓ 聊天列表数据库打开成功，版本: ${db.version}`);
                dbLogMessage(`数据库包含的表: ${Array.from(db.objectStoreNames).join(', ')}`);
                
                // 检查是否有聊天列表表
                const tableName = `chatList_${currentUserId}`;
                if (db.objectStoreNames.contains(tableName)) {
                    dbLogMessage(`✓ 找到聊天列表表: ${tableName}`);
                    
                    // 检查索引
                    const transaction = db.transaction([tableName], 'readonly');
                    const store = transaction.objectStore(tableName);
                    const indexNames = Array.from(store.indexNames);
                    dbLogMessage(`表索引: ${indexNames.join(', ')}`);
                } else {
                    dbLogMessage(`✗ 未找到聊天列表表: ${tableName}`);
                }
                
                db.close();
            };
            
            request.onerror = function(event) {
                dbLogMessage(`✗ 聊天列表数据库打开失败: ${event.target.error}`);
            };
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            log('WebSocket消息修复测试页面已加载');
            log(`当前用户ID: ${currentUserId}`);
            log('点击上方按钮开始测试...');
        };
    </script>
</body>
</html>
