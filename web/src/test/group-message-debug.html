<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>群聊消息调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>群聊消息存储和查询调试</h1>
    
    <div class="section">
        <h2>模拟WebSocket群聊消息</h2>
        <p>模拟接收到的WebSocket消息：</p>
        <pre id="mockMessage"></pre>
        <button onclick="simulateGroupMessage()">模拟接收群聊消息</button>
        <button onclick="queryGroupMessages()">查询群聊消息</button>
        <button onclick="checkDatabase()">检查数据库</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="section">
        <h2>调试日志</h2>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        const debugLog = document.getElementById('debugLog');
        const mockMessageElement = document.getElementById('mockMessage');

        // 模拟WebSocket消息
        const mockWebSocketMessage = {
            fromid: 10010,
            toid: 10009,
            groupID: 1,
            id: 453,
            typecode: 2,
            typecode2: 0,
            t: "2024-01-15T09:56:10.829Z",
            msg: "a/iYxGzcR1vKfRbgBKbM8w==",
            nickname: "测试用户10010"
        };

        mockMessageElement.textContent = JSON.stringify(mockWebSocketMessage, null, 2);

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            debugLog.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        function clearLog() {
            debugLog.innerHTML = '';
        }

        // 模拟解密函数
        function mockDecrypt(encryptedMsg) {
            // 简单的模拟解密，实际应该使用真正的解密函数
            return "测试群聊消息内容";
        }

        // 模拟群聊消息处理
        function simulateGroupMessage() {
            log('开始模拟群聊消息处理...', 'info');
            
            const data = mockWebSocketMessage;
            const currentUserId = '10009'; // 模拟当前用户ID
            localStorage.setItem('userId', currentUserId);
            
            log(`接收到群聊消息: groupID=${data.groupID}, fromid=${data.fromid}`, 'info');
            
            // 检查是否是自己发送的消息
            if (data.fromid && String(data.fromid) === String(currentUserId)) {
                log('这是自己发送的消息，应该跳过存储', 'warning');
                return;
            } else {
                log('这是其他用户发送的消息，应该存储到数据库', 'success');
            }
            
            // 模拟解密
            const decryptedMsg = mockDecrypt(data.msg);
            log(`消息解密结果: ${decryptedMsg}`, 'info');
            
            // 确保群组ID是字符串类型
            const groupId = (data.groupID || data.toid).toString();
            log(`群组ID (字符串): "${groupId}"`, 'info');
            
            // 模拟消息项
            const messageItem = {
                id: data.id || Date.now().toString(),
                typecode: data.typecode || 2,
                typecode2: data.typecode2 || 0,
                toid: data.toid || 0,
                fromid: data.fromid || 0,
                chatid: groupId, // 关键：确保是字符串
                t: data.t || new Date().toISOString(),
                msg: decryptedMsg,
                isRedRead: 0,
                idDel: 0,
                avatar: '',
                nickname: data.nickname || `用户${data.fromid}`,
                senderAvatar: '',
                senderNickname: data.nickname || `用户${data.fromid}`,
                lastMessage: decryptedMsg,
                timestamp: new Date(data.t || new Date()).getTime(),
                unreadCount: 1
            };
            
            log('准备存储的消息项:', 'info');
            log(JSON.stringify(messageItem, null, 2), 'info');
            
            // 模拟存储到IndexedDB
            storeMessageToIndexedDB(messageItem);
        }

        // 存储消息到IndexedDB
        function storeMessageToIndexedDB(messageItem) {
            const dbName = 'chatDb';
            const dbVersion = 2;
            const currentUserId = localStorage.getItem('userId') || 'default';
            const tableName = `chatData_${currentUserId}`;
            
            log(`打开数据库: ${dbName}, 表名: ${tableName}`, 'info');
            
            const request = indexedDB.open(dbName, dbVersion);
            
            request.onupgradeneeded = function(event) {
                const db = event.target.result;
                log('数据库需要升级，创建表...', 'warning');
                
                if (!db.objectStoreNames.contains(tableName)) {
                    const store = db.createObjectStore(tableName, { keyPath: 'id' });
                    store.createIndex('chatid', 'chatid', { unique: false });
                    store.createIndex('typecode', 'typecode', { unique: false });
                    store.createIndex('timestamp', 'timestamp', { unique: false });
                    log(`表 ${tableName} 创建成功`, 'success');
                }
            };
            
            request.onsuccess = function(event) {
                const db = event.target.result;
                log('数据库打开成功', 'success');
                
                const transaction = db.transaction([tableName], 'readwrite');
                const store = transaction.objectStore(tableName);
                
                const addRequest = store.add(messageItem);
                
                addRequest.onsuccess = function() {
                    log('消息存储成功！', 'success');
                    log(`存储的chatid: "${messageItem.chatid}"`, 'info');
                };
                
                addRequest.onerror = function() {
                    log(`消息存储失败: ${addRequest.error}`, 'error');
                };
                
                db.close();
            };
            
            request.onerror = function() {
                log(`数据库打开失败: ${request.error}`, 'error');
            };
        }

        // 查询群聊消息
        function queryGroupMessages() {
            const groupId = '1'; // 查询群组ID为1的消息
            log(`开始查询群组 ${groupId} 的消息...`, 'info');
            
            const dbName = 'chatDb';
            const currentUserId = localStorage.getItem('userId') || 'default';
            const tableName = `chatData_${currentUserId}`;
            
            const request = indexedDB.open(dbName, 2);
            
            request.onsuccess = function(event) {
                const db = event.target.result;
                
                if (!db.objectStoreNames.contains(tableName)) {
                    log(`表 ${tableName} 不存在`, 'error');
                    return;
                }
                
                const transaction = db.transaction([tableName], 'readonly');
                const store = transaction.objectStore(tableName);
                const index = store.index('chatid');
                
                log(`使用索引查询 chatid: "${groupId}"`, 'info');
                
                const queryRequest = index.getAll(groupId);
                
                queryRequest.onsuccess = function() {
                    const messages = queryRequest.result || [];
                    log(`查询到 ${messages.length} 条消息`, messages.length > 0 ? 'success' : 'warning');
                    
                    if (messages.length > 0) {
                        messages.forEach((msg, index) => {
                            log(`消息 ${index + 1}: chatid="${msg.chatid}", msg="${msg.msg}"`, 'info');
                        });
                    } else {
                        log('没有找到消息，开始调试查询...', 'warning');
                        debugQuery(store, groupId);
                    }
                };
                
                queryRequest.onerror = function() {
                    log(`查询失败: ${queryRequest.error}`, 'error');
                };
                
                db.close();
            };
        }

        // 调试查询
        function debugQuery(store, targetChatId) {
            const allRequest = store.getAll();
            
            allRequest.onsuccess = function() {
                const allMessages = allRequest.result || [];
                log(`数据库中总共有 ${allMessages.length} 条消息`, 'info');
                
                const groupMessages = allMessages.filter(msg => msg.typecode === 2);
                log(`其中群聊消息有 ${groupMessages.length} 条`, 'info');
                
                groupMessages.forEach((msg, index) => {
                    const msgChatId = msg.chatid?.toString();
                    const match = msgChatId === targetChatId.toString();
                    log(`群聊消息 ${index + 1}: chatid="${msgChatId}" ${match ? '✓匹配' : '✗不匹配'}`, match ? 'success' : 'warning');
                });
            };
        }

        // 检查数据库状态
        function checkDatabase() {
            log('检查数据库状态...', 'info');
            
            const currentUserId = localStorage.getItem('userId') || 'default';
            log(`当前用户ID: ${currentUserId}`, 'info');
            
            const request = indexedDB.open('chatDb', 2);
            
            request.onsuccess = function(event) {
                const db = event.target.result;
                log(`数据库版本: ${db.version}`, 'info');
                log(`包含的表: ${Array.from(db.objectStoreNames).join(', ')}`, 'info');
                
                const tableName = `chatData_${currentUserId}`;
                if (db.objectStoreNames.contains(tableName)) {
                    log(`✓ 表 ${tableName} 存在`, 'success');
                    
                    const transaction = db.transaction([tableName], 'readonly');
                    const store = transaction.objectStore(tableName);
                    log(`表的索引: ${Array.from(store.indexNames).join(', ')}`, 'info');
                } else {
                    log(`✗ 表 ${tableName} 不存在`, 'error');
                }
                
                db.close();
            };
        }

        // 页面加载时初始化
        window.onload = function() {
            log('群聊消息调试页面已加载', 'info');
            log('点击按钮开始测试...', 'info');
        };
    </script>
</body>
</html>
