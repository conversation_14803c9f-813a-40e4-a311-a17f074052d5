<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>WebSocket消息修复验证</h1>
    
    <div class="info status">
        <strong>修复内容验证：</strong>
        <ul>
            <li>✅ 修复了 getCurrentUserId 导出问题</li>
            <li>✅ 避免消息重复存储</li>
            <li>✅ 统一私聊消息chatid</li>
            <li>✅ 修复聊天列表数据库索引</li>
        </ul>
    </div>

    <div>
        <h2>功能测试</h2>
        <button onclick="testCurrentUserId()">测试获取当前用户ID</button>
        <button onclick="testPrivateChatId()">测试私聊ID生成</button>
        <button onclick="testMessageFiltering()">测试消息过滤逻辑</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div id="testLog" class="log"></div>

    <script>
        const log = document.getElementById('testLog');

        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            log.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            log.textContent = '';
        }

        // 模拟 getCurrentUserId 函数
        function getCurrentUserId() {
            return localStorage.getItem('userId') || sessionStorage.getItem('userId') || 'default';
        }

        // 模拟 generatePrivateChatId 函数
        function generatePrivateChatId(userId1, userId2) {
            const id1 = parseInt(userId1);
            const id2 = parseInt(userId2);
            const sortedIds = [id1, id2].sort((a, b) => a - b);
            return `private_${sortedIds[0]}_${sortedIds[1]}`;
        }

        // 模拟消息过滤逻辑
        function shouldSkipMessage(messageFromId, currentUserId) {
            return messageFromId && String(messageFromId) === String(currentUserId);
        }

        function testCurrentUserId() {
            logMessage('测试获取当前用户ID功能...');
            
            // 设置测试用户ID
            localStorage.setItem('userId', '12345');
            
            const userId = getCurrentUserId();
            if (userId === '12345') {
                logMessage(`获取用户ID成功: ${userId}`, 'success');
            } else {
                logMessage(`获取用户ID失败，期望: 12345，实际: ${userId}`, 'error');
            }

            // 测试默认值
            localStorage.removeItem('userId');
            sessionStorage.removeItem('userId');
            
            const defaultUserId = getCurrentUserId();
            if (defaultUserId === 'default') {
                logMessage(`默认用户ID正确: ${defaultUserId}`, 'success');
            } else {
                logMessage(`默认用户ID错误，期望: default，实际: ${defaultUserId}`, 'error');
            }

            // 恢复测试用户ID
            localStorage.setItem('userId', '12345');
        }

        function testPrivateChatId() {
            logMessage('测试私聊ID生成功能...');
            
            const chatId1 = generatePrivateChatId(123, 456);
            const chatId2 = generatePrivateChatId(456, 123);
            
            logMessage(`用户123和456的聊天ID: ${chatId1}`);
            logMessage(`用户456和123的聊天ID: ${chatId2}`);
            
            if (chatId1 === chatId2) {
                logMessage('双向唯一ID生成正确，两个方向生成的ID相同', 'success');
            } else {
                logMessage('双向唯一ID生成错误，两个方向生成的ID不同', 'error');
            }

            // 测试更多组合
            const testCases = [
                [100, 200],
                [999, 111],
                [1, 1000]
            ];

            testCases.forEach(([id1, id2]) => {
                const chatId_a = generatePrivateChatId(id1, id2);
                const chatId_b = generatePrivateChatId(id2, id1);
                if (chatId_a === chatId_b) {
                    logMessage(`用户${id1}和${id2}的双向ID一致: ${chatId_a}`, 'success');
                } else {
                    logMessage(`用户${id1}和${id2}的双向ID不一致: ${chatId_a} vs ${chatId_b}`, 'error');
                }
            });
        }

        function testMessageFiltering() {
            logMessage('测试消息过滤逻辑...');
            
            const currentUserId = '12345';
            localStorage.setItem('userId', currentUserId);
            
            // 测试场景
            const testMessages = [
                { fromid: 12345, description: '自己发送的消息' },
                { fromid: '12345', description: '自己发送的消息（字符串ID）' },
                { fromid: 67890, description: '其他用户发送的消息' },
                { fromid: null, description: '无发送者ID的消息' },
                { fromid: undefined, description: '未定义发送者ID的消息' }
            ];

            testMessages.forEach(msg => {
                const shouldSkip = shouldSkipMessage(msg.fromid, currentUserId);
                const expected = (msg.fromid && String(msg.fromid) === String(currentUserId));
                
                if (shouldSkip === expected) {
                    const action = shouldSkip ? '跳过存储' : '正常存储';
                    logMessage(`${msg.description}: ${action} ✓`, 'success');
                } else {
                    logMessage(`${msg.description}: 过滤逻辑错误`, 'error');
                }
            });

            logMessage('消息过滤逻辑测试完成');
        }

        // 页面加载时的初始化
        window.onload = function() {
            logMessage('WebSocket修复验证页面已加载');
            logMessage('所有导入问题已修复，getCurrentUserId 现在直接在本地定义');
            logMessage('点击上方按钮开始测试各项功能...');
            
            // 设置默认测试用户
            localStorage.setItem('userId', '12345');
            logMessage(`设置测试用户ID: ${localStorage.getItem('userId')}`);
        };
    </script>
</body>
</html>
