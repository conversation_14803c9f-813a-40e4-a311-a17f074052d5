<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单群聊测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>群聊消息存储查询测试</h1>
    
    <button onclick="testStore()">存储测试消息</button>
    <button onclick="testQuery()">查询消息</button>
    <button onclick="clearDB()">清空数据库</button>
    <button onclick="clearLog()">清空日志</button>
    
    <div id="log" class="log"></div>

    <script>
        const log = document.getElementById('log');
        
        function logMsg(msg, type = 'info') {
            const time = new Date().toLocaleTimeString();
            const color = type === 'success' ? 'green' : type === 'error' ? 'red' : type === 'warning' ? 'orange' : 'black';
            log.innerHTML += `<div style="color: ${color}">[${time}] ${msg}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            log.innerHTML = '';
        }
        
        // 测试存储
        function testStore() {
            logMsg('开始存储测试消息...', 'info');
            
            const testMessage = {
                id: 'test_' + Date.now(),
                typecode: 2, // 群聊
                typecode2: 0, // 文本
                fromid: 10010,
                toid: 10009,
                chatid: '1', // 关键：确保是字符串
                t: new Date().toISOString(),
                msg: '测试群聊消息',
                isRedRead: 0,
                idDel: 0,
                nickname: '测试用户',
                timestamp: Date.now()
            };
            
            logMsg(`准备存储消息，chatid类型: ${typeof testMessage.chatid}, 值: "${testMessage.chatid}"`, 'info');
            
            const request = indexedDB.open('chatDb', 2);
            
            request.onupgradeneeded = function(event) {
                const db = event.target.result;
                const tableName = 'chatData_default';
                
                if (!db.objectStoreNames.contains(tableName)) {
                    const store = db.createObjectStore(tableName, { keyPath: 'id' });
                    store.createIndex('chatid', 'chatid', { unique: false });
                    store.createIndex('typecode', 'typecode', { unique: false });
                    logMsg('创建数据库表和索引', 'success');
                }
            };
            
            request.onsuccess = function(event) {
                const db = event.target.result;
                const transaction = db.transaction(['chatData_default'], 'readwrite');
                const store = transaction.objectStore('chatData_default');
                
                const addRequest = store.add(testMessage);
                
                addRequest.onsuccess = function() {
                    logMsg('消息存储成功！', 'success');
                };
                
                addRequest.onerror = function() {
                    logMsg(`存储失败: ${addRequest.error}`, 'error');
                };
                
                db.close();
            };
            
            request.onerror = function() {
                logMsg(`数据库打开失败: ${request.error}`, 'error');
            };
        }
        
        // 测试查询
        function testQuery() {
            logMsg('开始查询测试...', 'info');
            
            const targetChatId = '1'; // 查询群组1的消息
            logMsg(`查询chatid: "${targetChatId}" (类型: ${typeof targetChatId})`, 'info');
            
            const request = indexedDB.open('chatDb', 2);
            
            request.onsuccess = function(event) {
                const db = event.target.result;
                const transaction = db.transaction(['chatData_default'], 'readonly');
                const store = transaction.objectStore('chatData_default');
                
                // 方法1：使用索引查询
                logMsg('方法1：使用chatid索引查询', 'info');
                const index = store.index('chatid');
                const indexRequest = index.getAll(targetChatId);
                
                indexRequest.onsuccess = function() {
                    const messages = indexRequest.result || [];
                    logMsg(`索引查询结果: ${messages.length} 条消息`, messages.length > 0 ? 'success' : 'warning');
                    
                    // 方法2：获取所有消息并过滤
                    logMsg('方法2：获取所有消息并过滤', 'info');
                    const allRequest = store.getAll();
                    
                    allRequest.onsuccess = function() {
                        const allMessages = allRequest.result || [];
                        logMsg(`数据库总消息数: ${allMessages.length}`, 'info');
                        
                        const filteredMessages = allMessages.filter(msg => {
                            const msgChatId = msg.chatid;
                            const match = String(msgChatId) === String(targetChatId);
                            logMsg(`消息chatid: "${msgChatId}" (类型: ${typeof msgChatId}) ${match ? '✓' : '✗'}`, match ? 'success' : 'warning');
                            return match;
                        });
                        
                        logMsg(`过滤后的消息数: ${filteredMessages.length}`, filteredMessages.length > 0 ? 'success' : 'error');
                        
                        if (filteredMessages.length > 0) {
                            filteredMessages.forEach(msg => {
                                logMsg(`找到消息: ${msg.msg}`, 'success');
                            });
                        }
                    };
                };
                
                db.close();
            };
        }
        
        // 清空数据库
        function clearDB() {
            logMsg('清空数据库...', 'warning');
            
            const deleteRequest = indexedDB.deleteDatabase('chatDb');
            
            deleteRequest.onsuccess = function() {
                logMsg('数据库已清空', 'success');
            };
            
            deleteRequest.onerror = function() {
                logMsg('清空数据库失败', 'error');
            };
        }
        
        window.onload = function() {
            logMsg('页面加载完成，点击按钮开始测试', 'info');
        };
    </script>
</body>
</html>
