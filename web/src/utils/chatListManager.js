// 聊天列表管理工具
import { addTabItem, getChatMessages, generatePrivateChatId, getCurrentUserId } from './db.js'

/**
 * 聊天列表数据结构
 * {
 *   id: String,              // 聊天列表项ID
 *   chatId: String,          // 聊天ID（群聊为群ID，私聊为双向唯一ID）
 *   type: String,            // 聊天类型 'group' | 'private'
 *   name: String,            // 显示名称
 *   avatar: String,          // 头像URL
 *   lastMessage: String,     // 最后一条消息
 *   lastTime: String,        // 最后消息时间
 *   timestamp: Number,       // 时间戳
 *   unreadCount: Number,     // 未读消息数
 *   targetId: Number,        // 目标ID（群ID或用户ID）
 *   isTop: Boolean,          // 是否置顶
 *   isMuted: Boolean         // 是否静音
 * }
 */

// 聊天列表表名
const getChatListTableName = () => `chatList_${getCurrentUserId()}`

// 数据库实例
let db = null

/**
 * 确保聊天列表数据库表存在
 * @returns {Promise<IDBDatabase>}
 */
const ensureChatListTable = async () => {
  if (db) return db

  return new Promise((resolve, reject) => {
    const request = indexedDB.open('ChatListDB', 1)

    request.onupgradeneeded = (event) => {
      db = event.target.result
      const tableName = getChatListTableName()

      if (!db.objectStoreNames.contains(tableName)) {
        const store = db.createObjectStore(tableName, { keyPath: 'chatId' })
        store.createIndex('chatType', 'chatType', { unique: false })
        store.createIndex('lastTime', 'lastTime', { unique: false })
        console.log('聊天列表表创建成功:', tableName)
      }
    }

    request.onsuccess = (event) => {
      db = event.target.result
      resolve(db)
    }

    request.onerror = () => {
      console.error('打开聊天列表数据库失败:', request.error)
      reject(request.error)
    }
  })
}

/**
 * 添加聊天列表项
 * @param {Object} chatItem - 聊天列表项
 * @returns {Promise<Object>}
 */
export const addChatListItem = async (chatItem) => {
  try {
    const database = await ensureChatListTable()
    const tableName = getChatListTableName()

    const chatListItem = {
      ...chatItem,
      createdAt: chatItem.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // 确保私聊列表项持久化存储
      isPersistent: true
    }

    return new Promise((resolve, reject) => {
      const transaction = database.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      
      const request = store.put(chatListItem)

      request.onsuccess = () => {
        console.log('聊天列表项添加成功:', chatListItem.chatId)
        resolve(chatListItem)
      }

      request.onerror = () => {
        console.error('添加聊天列表项失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('添加聊天列表项异常:', error)
    throw error
  }
}

/**
 * 更新聊天列表项
 * @param {string} chatId - 聊天ID
 * @param {Object} updateData - 更新数据
 * @returns {Promise<boolean>}
 */
export const updateChatListItem = async (chatId, updateData) => {
  try {
    const database = await ensureChatListTable()
    const tableName = getChatListTableName()

    return new Promise((resolve, reject) => {
      const transaction = database.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      
      const getRequest = store.get(chatId)
      
      getRequest.onsuccess = () => {
        const existingItem = getRequest.result
        if (!existingItem) {
          console.warn('聊天列表项不存在:', chatId)
          resolve(false)
          return
        }
        
        const updatedItem = {
          ...existingItem,
          ...updateData,
          updatedAt: new Date().toISOString()
        }
        
        const putRequest = store.put(updatedItem)
        
        putRequest.onsuccess = () => {
          console.log('聊天列表项更新成功:', chatId)
          resolve(true)
        }
        
        putRequest.onerror = () => {
          console.error('更新聊天列表项失败:', putRequest.error)
          reject(putRequest.error)
        }
      }
      
      getRequest.onerror = () => {
        console.error('获取聊天列表项失败:', getRequest.error)
        reject(getRequest.error)
      }
    })
  } catch (error) {
    console.error('更新聊天列表项异常:', error)
    throw error
  }
}

/**
 * 添加或更新聊天列表项
 * @param {Object} chatItem - 聊天列表项
 * @returns {Promise<Object>}
 */
export const addOrUpdateChatListItem = async (chatItem) => {
  try {
    const db = await ensureChatListTable()
    const tableName = getChatListTableName()
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      
      // 处理聊天列表项数据
      const processedItem = {
        id: chatItem.id || `${chatItem.type}_${chatItem.targetId}`,
        chatId: chatItem.chatId,
        type: chatItem.type, // 'group' | 'private'
        name: chatItem.name,
        avatar: chatItem.avatar || '',
        lastMessage: chatItem.lastMessage || '',
        lastTime: chatItem.lastTime || new Date().toISOString(),
        timestamp: chatItem.timestamp || Date.now(),
        unreadCount: chatItem.unreadCount || 0,
        targetId: chatItem.targetId,
        isTop: chatItem.isTop || false,
        isMuted: chatItem.isMuted || false
      }
      
      const request = store.put(processedItem)
      
      request.onsuccess = () => {
        console.log('聊天列表项保存成功:', processedItem.id)
        resolve(processedItem)
      }
      
      request.onerror = () => {
        console.error('聊天列表项保存失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('添加聊天列表项失败:', error)
    throw error
  }
}

/**
 * 获取聊天列表
 * @param {string} type - 聊天类型过滤 'group' | 'private' | null
 * @returns {Promise<Array>}
 */
export const getChatList = async (type = null) => {
  try {
    const db = await ensureChatListTable()
    const tableName = getChatListTableName()
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readonly')
      const store = transaction.objectStore(tableName)
      
      let request
      if (type) {
        const index = store.index('type')
        request = index.getAll(type)
      } else {
        request = store.getAll()
      }
      
      request.onsuccess = () => {
        const chatList = request.result || []
        
        // 按置顶和时间排序
        chatList.sort((a, b) => {
          // 置顶的在前
          if (a.isTop && !b.isTop) return -1
          if (!a.isTop && b.isTop) return 1
          
          // 按时间戳降序排序
          return b.timestamp - a.timestamp
        })
        
        console.log(`获取聊天列表成功: ${type || '全部'} - ${chatList.length}项`)
        resolve(chatList)
      }
      
      request.onerror = () => {
        console.error('获取聊天列表失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取聊天列表处理错误:', error)
    throw error
  }
}

/**
 * 删除聊天列表项
 * @param {string} chatListId - 聊天列表项ID
 * @returns {Promise<boolean>}
 */
export const deleteChatListItem = async (chatListId) => {
  try {
    const db = await ensureChatListTable()
    const tableName = getChatListTableName()
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      
      const request = store.delete(chatListId)
      
      request.onsuccess = () => {
        console.log('聊天列表项删除成功:', chatListId)
        resolve(true)
      }
      
      request.onerror = () => {
        console.error('聊天列表项删除失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('删除聊天列表项失败:', error)
    throw error
  }
}

/**
 * 更新聊天列表项的未读数量
 * @param {string} chatListId - 聊天列表项ID
 * @param {number} unreadCount - 未读数量
 * @returns {Promise<boolean>}
 */
export const updateUnreadCount = async (chatListId, unreadCount) => {
  try {
    const db = await ensureChatListTable()
    const tableName = getChatListTableName()
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      
      // 先获取现有项
      const getRequest = store.get(chatListId)
      
      getRequest.onsuccess = () => {
        const existingItem = getRequest.result
        if (!existingItem) {
          reject(new Error('聊天列表项不存在'))
          return
        }
        
        // 更新未读数量
        existingItem.unreadCount = unreadCount
        
        const putRequest = store.put(existingItem)
        
        putRequest.onsuccess = () => {
          console.log('未读数量更新成功:', chatListId, unreadCount)
          resolve(true)
        }
        
        putRequest.onerror = () => {
          console.error('未读数量更新失败:', putRequest.error)
          reject(putRequest.error)
        }
      }
      
      getRequest.onerror = () => {
        console.error('获取聊天列表项失败:', getRequest.error)
        reject(getRequest.error)
      }
    })
  } catch (error) {
    console.error('更新未读数量失败:', error)
    throw error
  }
}

/**
 * 更新聊天列表项的最后消息
 * @param {string} chatId - 聊天ID
 * @param {string} type - 聊天类型
 * @param {Object} messageData - 消息数据
 * @returns {Promise<boolean>}
 */
export const updateLastMessage = async (chatId, type, messageData) => {
  try {
    const db = await ensureChatListTable()
    const tableName = getChatListTableName()
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      const index = store.index('chatId')
      
      const request = index.get(chatId)
      
      request.onsuccess = () => {
        let chatItem = request.result
        
        if (!chatItem) {
          // 如果聊天列表项不存在，创建一个新的
          const targetId = type === 'group' ? chatId : 
            (chatId.startsWith('private_') ? chatId.split('_')[2] : chatId)
          
          chatItem = {
            id: `${type}_${targetId}`,
            chatId: chatId,
            type: type,
            name: messageData.senderNickname || `${type === 'group' ? '群聊' : '用户'}${targetId}`,
            avatar: messageData.senderAvatar || '',
            lastMessage: '',
            lastTime: '',
            timestamp: 0,
            unreadCount: 0,
            targetId: Number(targetId),
            isTop: false,
            isMuted: false
          }
        }
        
        // 更新最后消息信息
        chatItem.lastMessage = messageData.msg || messageData.content || ''
        chatItem.lastTime = messageData.t || messageData.timestamp || new Date().toISOString()
        chatItem.timestamp = new Date(chatItem.lastTime).getTime()
        
        // 如果不是当前用户发送的消息，增加未读数量
        const currentUserId = getCurrentUserId()
        const isOwnMessage = messageData.fromid && String(messageData.fromid) === String(currentUserId)
        if (!isOwnMessage) {
          chatItem.unreadCount = (chatItem.unreadCount || 0) + 1
        }
        
        const putRequest = store.put(chatItem)
        
        putRequest.onsuccess = () => {
          console.log('最后消息更新成功:', chatId)
          resolve(true)
        }
        
        putRequest.onerror = () => {
          console.error('最后消息更新失败:', putRequest.error)
          reject(putRequest.error)
        }
      }
      
      request.onerror = () => {
        console.error('获取聊天列表项失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('更新最后消息失败:', error)
    throw error
  }
}

/**
 * 清空聊天列表项的未读数量
 * @param {string} chatId - 聊天ID
 * @returns {Promise<boolean>}
 */
export const clearUnreadCount = async (chatId) => {
  try {
    const db = await ensureChatListTable()
    const tableName = getChatListTableName()
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      const index = store.index('chatId')
      
      const request = index.get(chatId)
      
      request.onsuccess = () => {
        const chatItem = request.result
        if (!chatItem) {
          resolve(true) // 如果不存在，认为成功
          return
        }
        
        chatItem.unreadCount = 0
        
        const putRequest = store.put(chatItem)
        
        putRequest.onsuccess = () => {
          console.log('未读数量清空成功:', chatId)
          resolve(true)
        }
        
        putRequest.onerror = () => {
          console.error('未读数量清空失败:', putRequest.error)
          reject(putRequest.error)
        }
      }
      
      request.onerror = () => {
        console.error('获取聊天列表项失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('清空未读数量失败:', error)
    throw error
  }
}

/**
 * 从群聊列表创建聊天列表项
 * @param {Array} groupList - 群聊列表
 * @returns {Promise<Array>}
 */
export const createChatListFromGroups = async (groupList) => {
  try {
    const chatListItems = []
    
    for (const group of groupList) {
      const chatItem = {
        id: `group_${group.id}`,
        chatId: String(group.id),
        type: 'group',
        name: group.groupName || group.name || `群聊${group.id}`,
        avatar: group.groupHeader || group.avatar || '',
        lastMessage: '暂无消息',
        lastTime: new Date().toISOString(),
        timestamp: Date.now(),
        unreadCount: 0,
        targetId: group.id,
        isTop: false,
        isMuted: false
      }
      
      // 尝试获取最后一条消息
      try {
        const messages = await getChatMessages(String(group.id), 1, 1, 'group')
        if (messages && messages.length > 0) {
          const lastMsg = messages[0]
          chatItem.lastMessage = lastMsg.msg || '暂无消息'
          chatItem.lastTime = lastMsg.t || lastMsg.timestamp
          chatItem.timestamp = new Date(chatItem.lastTime).getTime()
        }
      } catch (error) {
        console.warn('获取群聊最后消息失败:', error)
      }
      
      await addOrUpdateChatListItem(chatItem)
      chatListItems.push(chatItem)
    }
    
    console.log(`从群聊列表创建了 ${chatListItems.length} 个聊天列表项`)
    return chatListItems
  } catch (error) {
    console.error('从群聊列表创建聊天列表项失败:', error)
    throw error
  }
}

/**
 * 创建私聊列表项
 * @param {Object} userInfo - 用户信息
 * @param {number} currentUserId - 当前用户ID
 * @returns {Promise<Object>}
 */
export const createPrivateChatListItem = async (userInfo, currentUserId) => {
  try {
    const privateChatId = generatePrivateChatId(currentUserId, userInfo.id || userInfo.ID)
    
    const chatItem = {
      id: `private_${userInfo.id || userInfo.ID}`,
      chatId: privateChatId,
      type: 'private',
      name: userInfo.nickname || userInfo.name || `用户${userInfo.id || userInfo.ID}`,
      avatar: userInfo.avatar || userInfo.headImg || '',
      lastMessage: '开始聊天',
      lastTime: new Date().toISOString(),
      timestamp: Date.now(),
      unreadCount: 0,
      targetId: userInfo.id || userInfo.ID,
      isTop: false,
      isMuted: false
    }
    
    await addOrUpdateChatListItem(chatItem)
    console.log('私聊列表项创建成功:', chatItem.id)
    return chatItem
  } catch (error) {
    console.error('创建私聊列表项失败:', error)
    throw error
  }
}