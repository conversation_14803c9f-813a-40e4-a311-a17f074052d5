# WebSocket消息存储修复文档

## 问题描述

在原有的WebSocket消息处理逻辑中，存在以下问题：

1. **消息重复存储**：当用户发送消息后，WebSocket会给发送方和接收方都发送一条通知，导致发送方会收到自己发送的消息的WebSocket通知，然后又会存储一次到数据库，造成重复存储。

2. **私聊消息chatid不一致**：私聊消息在不同地方使用了不同的chatid生成逻辑，导致消息查询和聊天列表管理出现问题。

3. **聊天列表索引缺失**：聊天列表数据库表缺少必要的索引，导致查询失败。

## 修复方案

### 1. 避免消息重复存储

**修改文件**: `web/src/pinia/modules/websocket.js`

**修改内容**:
- 在 `handlePrivateMessage` 和 `handleGroupMessage` 函数中添加发送者检查
- 如果接收到的消息是当前用户发送的，则跳过存储到数据库的步骤

```javascript
// 获取当前用户ID
const { getCurrentUserId } = await import('@/utils/db.js')
const currentUserId = getCurrentUserId()

// 检查是否是自己发送的消息，如果是则跳过存储（避免重复存储）
if (data.fromid && String(data.fromid) === String(currentUserId)) {
  console.log('跳过自己发送的消息存储，避免重复:', data.fromid, currentUserId)
  return
}
```

### 2. 统一私聊消息chatid

**修改文件**: 
- `web/src/pinia/modules/websocket.js`
- `web/src/view/chatManager/components/MessagePanel.vue`

**修改内容**:
- 私聊消息统一使用 `generatePrivateChatId(userId1, userId2)` 生成双向唯一ID
- 确保发送和接收消息时使用相同的chatid逻辑

```javascript
// 私聊消息使用双向唯一ID
const { generatePrivateChatId } = await import('@/utils/db.js')
const privateChatId = generatePrivateChatId(currentUserId, data.fromid)

const messageItem = {
  // ...其他字段
  chatid: privateChatId, // 使用双向唯一ID
  // ...
}
```

### 3. 修复聊天列表数据库索引

**修改文件**: `web/src/utils/chatListManager.js`

**修改内容**:
- 在创建聊天列表表时添加必要的索引
- 添加 `chatId` 索引以支持按chatId查询

```javascript
if (!db.objectStoreNames.contains(tableName)) {
  const store = db.createObjectStore(tableName, { keyPath: 'chatId' })
  store.createIndex('chatId', 'chatId', { unique: false })
  store.createIndex('type', 'type', { unique: false })
  store.createIndex('lastTime', 'lastTime', { unique: false })
  console.log('聊天列表表创建成功:', tableName)
}
```

### 4. 优化消息发送后的处理

**修改文件**: `web/src/view/chatManager/components/MessagePanel.vue`

**修改内容**:
- 发送消息后使用 `updateLastMessage` 更新聊天列表
- 确保私聊和群聊都使用正确的chatid

```javascript
// 更新聊天列表的最后消息信息
const { updateLastMessage } = await import('@/utils/chatListManager.js')

await updateLastMessage(chatId, isGroup ? 'group' : 'private', {
  msg: content.trim(),
  t: new Date().toISOString(),
  fromid: currentUserId.value,
  senderNickname: currentUserInfo.nickname,
  senderAvatar: currentUserInfo.avatar
})
```

## 修复效果

### 修复前的问题
1. 发送消息后，数据库中会出现两条相同的消息记录
2. 私聊消息查询可能失败，因为chatid不一致
3. 聊天列表更新可能失败，因为索引缺失

### 修复后的效果
1. ✅ 消息不会重复存储，每条消息在数据库中只有一条记录
2. ✅ 私聊消息使用统一的双向唯一ID，确保查询一致性
3. ✅ 聊天列表正常更新，支持按chatId查询
4. ✅ 发送和接收消息都能正确显示在对话界面中

## 测试验证

可以使用以下测试文件验证修复效果：
- `web/src/test/websocket-message-fix-test.html` - 模拟测试页面

### 测试步骤
1. 打开测试页面
2. 点击"模拟接收私聊消息"按钮，验证其他用户消息正常存储
3. 点击"模拟接收自己的消息"按钮，验证自己的消息被跳过存储
4. 点击"检查数据库状态"按钮，验证数据库和索引正常

## 注意事项

1. **数据库版本升级**：如果用户已有旧版本的聊天列表数据库，可能需要处理数据库升级逻辑
2. **消息同步**：确保WebSocket连接稳定，避免消息丢失
3. **性能优化**：大量消息时注意数据库查询性能

## 相关文件

- `web/src/pinia/modules/websocket.js` - WebSocket消息处理
- `web/src/view/chatManager/components/MessagePanel.vue` - 消息面板组件
- `web/src/utils/chatListManager.js` - 聊天列表管理
- `web/src/utils/db.js` - 数据库操作工具
- `web/src/test/websocket-message-fix-test.html` - 测试页面
