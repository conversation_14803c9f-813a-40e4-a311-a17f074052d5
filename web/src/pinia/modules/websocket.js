import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import webSocketManager from '@/utils/websocket'
import { ElMessage, ElNotification } from 'element-plus'
import { getChatList, getChatMessages, markMessagesAsRead } from '@/utils/db.js'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const connectionStatus = ref('disconnected') // disconnected, connecting, connected
  const lastMessage = ref(null)
  const messageHistory = ref([])
  const unreadCount = ref(0)
  const autoReconnectEnabled = ref(true) // 自动重连开关

  // 计算属性
  const isConnected = computed(() => connectionStatus.value === 'connected')
  const isConnecting = computed(() => connectionStatus.value === 'connecting')

  // 初始化WebSocket连接
  const initConnection = async (token, persistent = true) => {
    try {
      connectionStatus.value = 'connecting'
      await webSocketManager.connect(token, persistent)
      connectionStatus.value = 'connected'

      // 设置消息处理器
      setupMessageHandlers()

      console.log('WebSocket连接已建立')
      return true
    } catch (error) {
      connectionStatus.value = 'disconnected'
      console.error('WebSocket连接失败:', error)

      // 持久连接模式下，连接失败也不抛出错误，让内部重连机制处理
      if (persistent && autoReconnectEnabled.value && token) {
        console.log('持久连接模式：连接失败，但重连机制将自动处理')
        return false // 返回false表示初始连接失败，但不阻止后续流程
      }

      throw error
    }
  }

  // 设置消息处理器
  const setupMessageHandlers = () => {
    // 处理所有消息
    webSocketManager.addMessageHandler('all', (data) => {
      lastMessage.value = data
      messageHistory.value.push({
        ...data,
        timestamp: new Date().toISOString()
      })
      
      // 限制消息历史记录数量
      if (messageHistory.value.length > 100) {
        messageHistory.value = messageHistory.value.slice(-100)
      }
      
      // 处理特定类型的消息
      handleSpecificMessage(data)
    })
  }

  // 处理特定类型的消息
  const handleSpecificMessage = (data) => {
    switch (data.type) {
      case 1: // 私聊消息
        handlePrivateMessage(data)
        break
      case 2: // 群聊消息
        handleGroupMessage(data)
        break
      case 3: // 好友申请
        handleFriendApply(data)
        break
      case 4: // 通过好友申请
        handleFriendApplyPass(data)
        break
      case 5: // 拒绝好友申请
        handleFriendApplyReject(data)
        break
      case 6: // 删除好友
        handleFriendDelete(data)
        break
      case 23: // 用户上线
        handleUserOnline(data)
        break
      case 24: // 用户下线
        handleUserOffline(data)
        break
      default:
        console.log('收到未处理的消息类型:', data.type, data)
    }
  }

  // 处理私聊消息
  const handlePrivateMessage = async (data) => {
    try {
      console.log('Pinia处理私聊消息:', data)

      // 获取当前用户ID
      const currentUserId = localStorage.getItem('userId') || sessionStorage.getItem('userId') || 'default'

      // 检查是否是自己发送的消息，如果是则跳过存储（避免重复存储）
      if (data.fromid && String(data.fromid) === String(currentUserId)) {
        console.log('跳过自己发送的私聊消息存储，避免重复:', data.fromid, currentUserId)
        return
      }

      // 解密消息内容
      let decryptedMsg = data.msg
      if (data.msg && typeof data.msg === 'string') {
        try {
          const { decryptAESBase64 } = await import('@/utils/decrypt.js')
          decryptedMsg = decryptAESBase64(data.msg)
          console.log('Pinia消息解密成功:', { original: data.msg, decrypted: decryptedMsg })
        } catch (decryptError) {
          console.warn('Pinia消息解密失败，使用原始消息:', decryptError)
          decryptedMsg = data.msg
        }
      }

      // 保存到数据库
      const { addTabItem, generatePrivateChatId } = await import('@/utils/db.js')
      const privateChatId = generatePrivateChatId(currentUserId, data.fromid)

      const messageItem = {
        id: data.id || Date.now().toString(),
        typecode: data.typecode || 1, // 私聊
        typecode2: data.typecode2 || 0,
        toid: data.toid || 0,
        fromid: data.fromid || 0,
        chatid: privateChatId, // 私聊时使用双向唯一ID
        t: data.t || new Date().toISOString(),
        msg: decryptedMsg,
        isRedRead: 0, // 未读
        idDel: 0,
        avatar: data.avatar || '',
        nickname: data.nickname || `用户${data.fromid}`,
        senderAvatar: data.senderAvatar || '',
        senderNickname: data.senderNickname || `用户${data.fromid}`,
        lastMessage: decryptedMsg,
        timestamp: new Date(data.t || new Date()).getTime(),
        unreadCount: 1
      }

      await addTabItem(messageItem)
      console.log('Pinia私聊消息已保存到数据库')

      // 更新聊天列表
      const { updateLastMessage } = await import('@/utils/chatListManager.js')
      await updateLastMessage(privateChatId, 'private', {
        msg: decryptedMsg,
        t: data.t || new Date().toISOString(),
        fromid: data.fromid,
        senderNickname: data.senderNickname || data.nickname || `用户${data.fromid}`,
        senderAvatar: data.senderAvatar || data.avatar || ''
      })

      // 立即触发私聊列表更新事件
      try {
        // 发射自定义事件通知私聊列表更新
        const updateEvent = new CustomEvent('privateMessageUpdate', {
          detail: {
            userId: data.fromid,
            messageData: {
              msg: decryptedMsg,
              t: data.t || new Date().toISOString(),
              typecode2: data.typecode2 || 0,
              senderNickname: messageItem.senderNickname,
              content: decryptedMsg,
              timestamp: new Date().toISOString(),
              type: data.typecode2 || 0
            }
          }
        })
        window.dispatchEvent(updateEvent)
        console.log('已发射私聊消息更新事件:', updateEvent.detail)
      } catch (error) {
        console.warn('发射私聊更新事件失败:', error)
      }

      unreadCount.value++
      ElNotification({
        title: '新消息',
        message: `收到来自${messageItem.senderNickname}的私聊消息: ${decryptedMsg.substring(0, 20)}${decryptedMsg.length > 20 ? '...' : ''}`,
        type: 'info',
        duration: 3000
      })
    } catch (error) {
      console.error('Pinia处理私聊消息失败:', error)
    }
  }

  // 处理群聊消息
  const handleGroupMessage = async (data) => {
    try {
      console.log('Pinia处理群聊消息:', data)

      // 获取当前用户ID
      const currentUserId = localStorage.getItem('userId') || sessionStorage.getItem('userId') || 'default'

      // 检查是否是自己发送的消息，如果是则跳过存储（避免重复存储）
      if (data.fromid && String(data.fromid) === String(currentUserId)) {
        console.log('跳过自己发送的群聊消息存储，避免重复:', data.fromid, currentUserId)
        return
      }

      // 解密消息内容
      let decryptedMsg = data.msg
      if (data.msg && typeof data.msg === 'string') {
        try {
          const { decryptAESBase64 } = await import('@/utils/decrypt.js')
          decryptedMsg = decryptAESBase64(data.msg)
          console.log('Pinia群聊消息解密成功:', { original: data.msg, decrypted: decryptedMsg })
        } catch (decryptError) {
          console.warn('Pinia群聊消息解密失败，使用原始消息:', decryptError)
          decryptedMsg = data.msg
        }
      }

      // 从群成员信息中获取发送者头像信息
      const { getUserInfoFromGroupMembers } = await import('@/utils/avatarService.js')
      const senderInfo = await getUserInfoFromGroupMembers(data.fromid, data.groupID)

      // 保存到数据库
      const { addTabItem } = await import('@/utils/db.js')

      // 确保群组ID是字符串类型
      const groupId = (data.groupID || data.toid).toString()

      const messageItem = {
        id: data.id || Date.now().toString(),
        typecode: data.typecode || 2, // 群聊
        typecode2: data.typecode2 || 0,
        toid: data.toid || 0,
        fromid: data.fromid || 0,
        chatid: groupId, // 群聊时chatid是群组ID（字符串）
        t: data.t || new Date().toISOString(),
        msg: decryptedMsg,
        isRedRead: 0, // 未读
        idDel: 0,
        avatar: senderInfo.avatar,
        nickname: data.nickname || senderInfo.nickname || `群组${data.groupID}`,
        senderAvatar: senderInfo.avatar,
        senderNickname: senderInfo.nickname,
        lastMessage: decryptedMsg,
        timestamp: new Date(data.t || new Date()).getTime(),
        unreadCount: 1
      }

      await addTabItem(messageItem)
      console.log('Pinia群聊消息已保存到数据库')

      // 更新聊天列表
      const { updateLastMessage } = await import('@/utils/chatListManager.js')
      await updateLastMessage(groupId, 'group', {
        msg: decryptedMsg,
        t: data.t || new Date().toISOString(),
        fromid: data.fromid,
        senderNickname: senderInfo.nickname || data.nickname || `用户${data.fromid}`,
        senderAvatar: senderInfo.avatar || ''
      })

      // 注释掉Pinia中的事件发射，避免与MessagePanel重复处理
      // MessagePanel组件会在接收到消息时发射更新事件
      // try {
      //   const updateEvent = new CustomEvent('groupMessageUpdate', {
      //     detail: {
      //       groupId: data.groupID || data.toid,
      //       messageData: {
      //         msg: decryptedMsg,
      //         t: data.t || new Date().toISOString(),
      //         typecode2: data.typecode2 || 0,
      //         senderNickname: senderInfo.nickname
      //       }
      //     }
      //   })
      //   window.dispatchEvent(updateEvent)
      //   console.log('已发射群聊消息更新事件:', updateEvent.detail)
      // } catch (error) {
      //   console.warn('发射群聊更新事件失败:', error)
      // }

      unreadCount.value++
      ElNotification({
        title: '群消息',
        message: `收到来自${messageItem.senderNickname}的群聊消息: ${decryptedMsg.substring(0, 20)}${decryptedMsg.length > 20 ? '...' : ''}`,
        type: 'info',
        duration: 3000
      })
    } catch (error) {
      console.error('Pinia处理群聊消息失败:', error)
    }
  }

  // 处理好友申请
  const handleFriendApply = (data) => {
    ElNotification({
      title: '好友申请',
      message: `收到新的好友申请`,
      type: 'warning',
      duration: 5000
    })
  }

  // 处理好友申请通过
  const handleFriendApplyPass = (data) => {
    ElMessage({
      message: '好友申请已通过',
      type: 'success'
    })
  }

  // 处理好友申请拒绝
  const handleFriendApplyReject = (data) => {
    ElMessage({
      message: '好友申请被拒绝',
      type: 'warning'
    })
  }

  // 处理删除好友
  const handleFriendDelete = (data) => {
    ElMessage({
      message: '好友关系已解除',
      type: 'info'
    })
  }

  // 处理用户上线
  const handleUserOnline = (data) => {
    console.log('用户上线:', data)
  }

  // 处理用户下线
  const handleUserOffline = (data) => {
    console.log('用户下线:', data)
  }

  // 发送消息
  const sendMessage = (data) => {
    if (!isConnected.value) {
      ElMessage.error('WebSocket未连接，无法发送消息')
      return false
    }
    
    return webSocketManager.send(data)
  }

  // 关闭连接
  const closeConnection = () => {
    // 禁用自动重连再关闭连接
    autoReconnectEnabled.value = false
    webSocketManager.close()
    connectionStatus.value = 'disconnected'
    lastMessage.value = null
    console.log('WebSocket连接已关闭')
  }

  // 清除未读计数
  const clearUnreadCount = () => {
    unreadCount.value = 0
  }

  // 清除消息历史
  const clearMessageHistory = () => {
    messageHistory.value = []
    lastMessage.value = null
  }

  // 获取连接状态
  const getConnectionStatus = () => {
    return webSocketManager.getStatus()
  }

  // 重新连接
  const reconnect = async (token, persistent = true) => {
    if (!token) {
      console.error('重连失败：token不能为空')
      throw new Error('token不能为空')
    }

    // 启用自动重连
    autoReconnectEnabled.value = true

    if (connectionStatus.value === 'connecting') {
      console.log('正在连接中，请稍候...')
      return
    }

    try {
      await initConnection(token, persistent)
    } catch (error) {
      console.error('重连失败:', error)
      throw error
    }
  }

  // 启用持久连接
  const enablePersistentConnection = () => {
    autoReconnectEnabled.value = true
    webSocketManager.enablePersistentConnection()
    console.log('持久连接已启用')
  }

  // 禁用持久连接
  const disablePersistentConnection = () => {
    webSocketManager.disablePersistentConnection()
    console.log('持久连接已禁用')
  }

  // 强制重连
  const forceReconnect = async (token = null) => {
    try {
      connectionStatus.value = 'connecting'

      // 如果没有传入token，尝试从用户store获取
      let useToken = token
      if (!useToken) {
        try {
          const { useUserStore } = await import('@/pinia/modules/user')
          const userStore = useUserStore()
          useToken = userStore.token
        } catch (error) {
          console.error('获取用户token失败:', error)
        }
      }

      await webSocketManager.forceReconnect(useToken)
      connectionStatus.value = 'connected'
      console.log('强制重连成功')
    } catch (error) {
      connectionStatus.value = 'disconnected'
      console.error('强制重连失败:', error)
      throw error
    }
  }
  
  // 启用自动重连
  const enableAutoReconnect = () => {
    autoReconnectEnabled.value = true
  }

  // 禁用自动重连
  const disableAutoReconnect = () => {
    autoReconnectEnabled.value = false
  }

  // 获取聊天列表
  const loadChatList = async () => {
    try {
      const chatList = await getChatList()
      console.log('加载聊天列表成功:', chatList.length)
      return chatList
    } catch (error) {
      console.error('加载聊天列表失败:', error)
      ElMessage.error('加载聊天列表失败')
      return []
    }
  }

  // 获取聊天消息
  const loadChatMessages = async (chatId, page = 1, size = 20) => {
    try {
      const messages = await getChatMessages(chatId, page, size)
      console.log(`加载聊天消息成功: chatId=${chatId}, 数量=${messages.length}`)
      return messages
    } catch (error) {
      console.error('加载聊天消息失败:', error)
      ElMessage.error('加载聊天消息失败')
      return []
    }
  }

  // 标记消息为已读
  const markChatAsRead = async (chatId) => {
    try {
      await markMessagesAsRead(chatId)
      console.log(`标记聊天为已读: chatId=${chatId}`)
      // 减少未读计数（这里可以根据实际情况调整）
      if (unreadCount.value > 0) {
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
      return true
    } catch (error) {
      console.error('标记消息为已读失败:', error)
      ElMessage.error('标记消息为已读失败')
      return false
    }
  }

  return {
    // 状态
    connectionStatus,
    lastMessage,
    messageHistory,
    unreadCount,
    autoReconnectEnabled,

    // 计算属性
    isConnected,
    isConnecting,

    // 方法
    initConnection,
    setupMessageHandlers,
    sendMessage,
    closeConnection,
    clearUnreadCount,
    clearMessageHistory,
    getConnectionStatus,
    reconnect,
    enableAutoReconnect,
    disableAutoReconnect,
    enablePersistentConnection,
    disablePersistentConnection,
    forceReconnect,

    // 数据库相关方法
    loadChatList,
    loadChatMessages,
    markChatAsRead
  }
})